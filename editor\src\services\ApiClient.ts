/**
 * 统一API客户端
 * 提供与后端微服务的统一通信接口
 */
import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { EventEmitter } from '../utils/EventEmitter';

export interface ApiConfig {
  baseURL: string;
  timeout: number;
  enableCache: boolean;
  cacheTimeout: number;
  retryCount: number;
  retryDelay: number;
}

export interface ApiResponse<T = any> {
  data: T;
  message?: string;
  code?: number;
  success: boolean;
}

export interface RequestOptions extends AxiosRequestConfig {
  useCache?: boolean;
  skipAuth?: boolean;
  retryCount?: number;
}

/**
 * API客户端类
 */
export class ApiClient extends EventEmitter {
  private instance: AxiosInstance;
  private config: ApiConfig;
  private cache: Map<string, { data: any; timestamp: number }> = new Map();
  private authToken: string | null = null;

  constructor(config?: Partial<ApiConfig>) {
    super();

    // 默认配置
    this.config = {
      baseURL: this.getApiBaseUrl(),
      timeout: 30000,
      enableCache: true,
      cacheTimeout: 5 * 60 * 1000, // 5分钟
      retryCount: 3,
      retryDelay: 1000,
      ...config
    };

    // 创建axios实例
    this.instance = axios.create({
      baseURL: this.config.baseURL,
      timeout: this.config.timeout,
      headers: {
        'Content-Type': 'application/json; charset=utf-8',
        'Accept': 'application/json',
        // 移除 Accept-Charset 请求头，因为它是浏览器禁止的不安全请求头
        // 浏览器会自动处理字符编码，无需手动设置
      },
    });

    this.setupInterceptors();
    this.loadAuthToken();
  }

  /**
   * 获取API基础URL
   */
  private getApiBaseUrl(): string {
    try {
      // 优先使用环境变量
      try {
        if (typeof window !== 'undefined') {
          const env = (window as any).__ENV__;
          if (env && env.REACT_APP_API_URL) {
            console.log('使用window.__ENV__中的API URL:', env.REACT_APP_API_URL);
            return env.REACT_APP_API_URL;
          }
        }
      } catch (windowEnvError) {
        console.warn('⚠️ 访问window.__ENV__失败:', windowEnvError);
      }

      // 检查Vite环境变量
      try {
        if (typeof import.meta !== 'undefined' && import.meta.env?.VITE_API_URL) {
          console.log('使用Vite环境变量API URL:', import.meta.env.VITE_API_URL);
          return import.meta.env.VITE_API_URL;
        }
      } catch (viteEnvError) {
        console.warn('⚠️ 访问Vite环境变量失败:', viteEnvError);
      }

      // 检查传统环境变量
      try {
        if (process.env.REACT_APP_API_URL) {
          console.log('使用REACT_APP_API_URL:', process.env.REACT_APP_API_URL);
          return process.env.REACT_APP_API_URL;
        }
      } catch (processEnvError) {
        console.warn('⚠️ 访问process.env失败:', processEnvError);
      }

      // 检查当前页面的协议和主机
      if (typeof window !== 'undefined') {
        const { protocol, hostname, port } = window.location;

        // 开发环境：如果是localhost，使用3000端口的API
        if (hostname === 'localhost' || hostname === '127.0.0.1') {
          const apiUrl = `${protocol}//${hostname}:3000/api`;
          console.log('开发环境使用API URL:', apiUrl);
          return apiUrl;
        }

        // 生产环境：如果是其他域名，使用相对路径
        const apiUrl = '/api';
        console.log('生产环境使用API URL:', apiUrl);
        return apiUrl;
      }
    } catch (error) {
      console.warn('获取API URL失败，使用默认配置:', error);
    }

    // 默认使用相对路径
    console.log('使用默认API URL: /api');
    return '/api';
  }

  /**
   * 设置请求和响应拦截器
   */
  private setupInterceptors(): void {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        // 检查是否跳过认证
        const skipAuth = (config as any).skipAuth;

        // 添加认证头（除非明确跳过）
        if (!skipAuth && this.authToken && !config.headers.Authorization) {
          config.headers.Authorization = `Bearer ${this.authToken}`;
        }

        // 添加请求ID
        config.headers['X-Request-ID'] = this.generateRequestId();

        this.emit('request:start', config);
        return config;
      },
      (error) => {
        this.emit('request:error', error);
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response) => {
        this.emit('response:success', response);
        return response;
      },
      async (error) => {
        const originalRequest = error.config;

        // 避免重复重试
        if (originalRequest._retry) {
          this.handleResponseError(error);
          return Promise.reject(error);
        }

        // 对于网络错误或5xx错误，尝试重试
        if (this.shouldRetry(error)) {
          originalRequest._retry = true;

          // 等待一段时间后重试
          await this.delay(1000);

          try {
            return await this.instance(originalRequest);
          } catch (retryError) {
            this.handleResponseError(retryError);
            return Promise.reject(retryError);
          }
        }

        this.handleResponseError(error);
        return Promise.reject(error);
      }
    );
  }

  /**
   * 判断是否应该重试请求
   */
  private shouldRetry(error: any): boolean {
    // 网络错误
    if (!error.response) {
      return true;
    }

    // 5xx服务器错误
    if (error.response.status >= 500) {
      return true;
    }

    // 429 请求过多
    if (error.response.status === 429) {
      return true;
    }

    return false;
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 处理响应错误
   */
  private handleResponseError(error: any): void {
    const { response, config } = error;

    // 记录错误信息
    const requestId = config?.headers?.['X-Request-ID'] || 'unknown';
    const url = config?.url || 'unknown';
    const method = config?.method?.toUpperCase() || 'UNKNOWN';

    if (response) {
      // 提取错误信息
      const errorMessage = response.data?.message || response.data?.error || response.statusText;

      switch (response.status) {
        case 400:
          // 请求参数错误
          console.warn(`[${requestId}] 请求参数错误 (400): ${method} ${url}`, errorMessage);
          this.emit('request:bad_request', { error, message: errorMessage });
          break;
        case 401:
          // 未授权，清除token并跳转登录
          console.warn(`[${requestId}] 认证失败 (401): ${method} ${url}`, errorMessage);
          this.clearAuthToken();
          this.emit('auth:unauthorized', { error, message: errorMessage });
          break;
        case 403:
          // 禁止访问
          console.warn(`[${requestId}] 访问被禁止 (403): ${method} ${url}`, errorMessage);
          this.emit('auth:forbidden', { error, message: errorMessage });
          break;
        case 404:
          // 资源未找到
          console.debug(`[${requestId}] 资源未找到 (404): ${method} ${url}`, errorMessage);
          this.emit('response:not_found', { error, message: errorMessage });
          break;
        case 409:
          // 冲突错误（如用户名或邮箱已存在）
          console.warn(`[${requestId}] 资源冲突 (409): ${method} ${url}`, errorMessage);
          this.emit('response:conflict', { error, message: errorMessage });
          break;
        case 422:
          // 数据验证错误
          console.warn(`[${requestId}] 数据验证错误 (422): ${method} ${url}`, errorMessage);
          this.emit('validation:error', { error, message: errorMessage });
          break;
        case 500:
        case 502:
        case 503:
        case 504:
          // 服务器错误
          console.error(`[${requestId}] 服务器错误 (${response.status}): ${method} ${url}`, errorMessage);
          this.emit('server:error', { error, message: errorMessage, status: response.status });
          break;
        default:
          console.warn(`[${requestId}] HTTP错误 (${response.status}): ${method} ${url}`, errorMessage);
          this.emit('response:error', { error, message: errorMessage, status: response.status });
      }
    } else {
      // 网络错误
      const networkError = error.code === 'ECONNABORTED' ? '请求超时' : '网络连接失败';
      console.error(`[${requestId}] 网络错误: ${method} ${url}`, networkError);
      this.emit('network:error', { error, message: networkError });
    }
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 加载认证token
   */
  private loadAuthToken(): void {
    if (typeof window !== 'undefined') {
      // 优先从localStorage获取，如果没有则从sessionStorage获取
      this.authToken = localStorage.getItem('token') || sessionStorage.getItem('token');
    }
  }

  /**
   * 设置认证token
   */
  public setAuthToken(token: string, remember: boolean = false): void {
    this.authToken = token;
    if (typeof window !== 'undefined') {
      if (remember) {
        // 记住我：使用localStorage，持久化存储
        localStorage.setItem('token', token);
        localStorage.setItem('rememberMe', 'true');
        sessionStorage.removeItem('token');
      } else {
        // 不记住：使用sessionStorage，会话结束后清除
        sessionStorage.setItem('token', token);
        localStorage.removeItem('rememberMe');
        localStorage.removeItem('token');
      }
    }
    this.emit('auth:token:set', token);
  }

  /**
   * 清除认证token
   */
  public clearAuthToken(): void {
    this.authToken = null;
    if (typeof window !== 'undefined') {
      localStorage.removeItem('token');
      sessionStorage.removeItem('token');
      localStorage.removeItem('rememberMe');
    }
    this.emit('auth:token:clear');
  }

  /**
   * 检查API服务是否可用
   */
  async checkHealth(): Promise<boolean> {
    try {
      const response = await this.get('/health', { skipAuth: true });
      return response.success === true;
    } catch (error) {
      console.warn('API健康检查失败:', error);
      return false;
    }
  }

  /**
   * 获取缓存键
   */
  private getCacheKey(url: string, params?: any): string {
    const paramStr = params ? JSON.stringify(params) : '';
    return `${url}:${paramStr}`;
  }

  /**
   * 检查缓存
   */
  private checkCache(key: string): any | null {
    if (!this.config.enableCache) return null;

    const cached = this.cache.get(key);
    if (cached) {
      const now = Date.now();
      if (now - cached.timestamp < this.config.cacheTimeout) {
        return cached.data;
      } else {
        this.cache.delete(key);
      }
    }
    return null;
  }

  /**
   * 设置缓存
   */
  private setCache(key: string, data: any): void {
    if (this.config.enableCache) {
      this.cache.set(key, {
        data,
        timestamp: Date.now()
      });
    }
  }

  /**
   * 发送GET请求
   */
  public async get<T = any>(url: string, options: RequestOptions = {}): Promise<ApiResponse<T>> {
    const cacheKey = this.getCacheKey(url, options.params);
    
    // 检查缓存
    if (options.useCache !== false) {
      const cached = this.checkCache(cacheKey);
      if (cached) {
        return cached;
      }
    }

    const response = await this.instance.get<ApiResponse<T>>(url, options);
    
    // 设置缓存
    if (options.useCache !== false) {
      this.setCache(cacheKey, response.data);
    }

    return response.data;
  }

  /**
   * 发送POST请求
   */
  public async post<T = any>(url: string, data?: any, options: RequestOptions = {}): Promise<ApiResponse<T>> {
    const response = await this.instance.post<ApiResponse<T>>(url, data, options);
    return response.data;
  }

  /**
   * 发送PUT请求
   */
  public async put<T = any>(url: string, data?: any, options: RequestOptions = {}): Promise<ApiResponse<T>> {
    const response = await this.instance.put<ApiResponse<T>>(url, data, options);
    return response.data;
  }

  /**
   * 发送PATCH请求
   */
  public async patch<T = any>(url: string, data?: any, options: RequestOptions = {}): Promise<ApiResponse<T>> {
    const response = await this.instance.patch<ApiResponse<T>>(url, data, options);
    return response.data;
  }

  /**
   * 发送DELETE请求
   */
  public async delete<T = any>(url: string, options: RequestOptions = {}): Promise<ApiResponse<T>> {
    const response = await this.instance.delete<ApiResponse<T>>(url, options);
    return response.data;
  }

  /**
   * 上传文件
   */
  public async upload<T = any>(url: string, file: File, options: RequestOptions = {}): Promise<ApiResponse<T>> {
    const formData = new FormData();
    formData.append('file', file);

    const config: AxiosRequestConfig = {
      ...options,
      headers: {
        'Content-Type': 'multipart/form-data',
        ...options.headers,
      },
    };

    const response = await this.instance.post<ApiResponse<T>>(url, formData, config);
    return response.data;
  }

  /**
   * 清除缓存
   */
  public clearCache(): void {
    this.cache.clear();
    this.emit('cache:cleared');
  }

  /**
   * 获取实例配置
   */
  public getConfig(): ApiConfig {
    return { ...this.config };
  }

  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<ApiConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.instance.defaults.baseURL = this.config.baseURL;
    this.instance.defaults.timeout = this.config.timeout;
    this.emit('config:updated', this.config);
  }
}

// 创建默认实例
export const apiClient = new ApiClient();

// 导出类型
export default ApiClient;
