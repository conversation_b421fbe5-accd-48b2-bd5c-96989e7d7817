/**
 * 视口组件
 */
import React, { useRef, useEffect } from 'react';
import { Button, Tooltip, Radio } from 'antd';
import {
  BorderOutlined,
  FullscreenOutlined,
  CameraOutlined,
  AimOutlined,
  DragOutlined,
  ScissorOutlined,
  RotateLeftOutlined} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
import { setViewportMode, setRenderMode, ViewportMode, RenderMode } from '../../store/ui/uiSlice';
import engineService from '../../services/EngineService';
import './Viewport.less';

const Viewport: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { viewportMode, renderMode } = useSelector((state: RootState) => state.ui);
  const { entities, selectedEntityId } = useSelector((state: RootState) => state.scene);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  
  // 初始化引擎
  useEffect(() => {
    if (!canvasRef.current || !containerRef.current) {
      console.warn('⚠️ Canvas或容器引用不可用，跳过引擎初始化');
      return;
    }

    // 使用EngineService初始化引擎
    const initializeEngine = async () => {
      try {
        console.log('🚀 开始初始化视口引擎...');

        // 验证canvas元素
        const canvas = canvasRef.current;
        if (!canvas) {
          throw new Error('Canvas元素不可用');
        }

        await engineService.initialize(canvas);

        // 获取引擎实例
        const engine = engineService.getEngine();
        if (!engine) {
          console.warn('⚠️ 引擎实例为空，使用模拟模式');
          return;
        }

        console.log('✅ 引擎初始化成功，创建场景...');

        // 创建场景（使用try-catch包装）
        try {
          const world = engine.getWorld();
          if (world && typeof world.createScene === 'function') {
            const scene = world.createScene('MainScene');
            console.log('✅ 场景创建成功:', scene);
          } else {
            console.warn('⚠️ 世界对象无效或缺少createScene方法');
          }
        } catch (sceneError) {
          console.warn('⚠️ 场景创建失败，但引擎将继续运行:', sceneError);
        }

        // 启动引擎
        try {
          engineService.start();
          console.log('✅ 引擎启动成功');
        } catch (startError) {
          console.warn('⚠️ 引擎启动失败:', startError);
        }

        // 处理窗口大小变化
        const handleResize = () => {
          try {
            if (!containerRef.current || !canvasRef.current) return;

            const width = containerRef.current.clientWidth;
            const height = containerRef.current.clientHeight;

            // 设置canvas大小
            canvasRef.current.width = width;
            canvasRef.current.height = height;
            canvasRef.current.style.width = width + 'px';
            canvasRef.current.style.height = height + 'px';

            // 设置渲染器大小
            const currentEngine = engineService.getEngine();
            if (currentEngine) {
              const renderer = currentEngine.getRenderer();
              if (renderer && typeof renderer.setSize === 'function') {
                renderer.setSize(width, height);
              }

              // 如果是模拟引擎，重新绘制场景
              if ('redraw' in currentEngine && typeof (currentEngine as any).redraw === 'function') {
                (currentEngine as any).redraw();
              }
            }
          } catch (resizeError) {
            console.warn('⚠️ 窗口大小调整失败:', resizeError);
          }
        };

        window.addEventListener('resize', handleResize);

        // 初始调用一次以设置正确的大小
        handleResize();

        // 清理函数
        return () => {
          try {
            window.removeEventListener('resize', handleResize);
            engineService.stop();
          } catch (cleanupError) {
            console.warn('⚠️ 清理过程中出现错误:', cleanupError);
          }
        };
      } catch (error) {
        console.error('❌ 引擎初始化失败:', error);
        // 不阻止组件渲染，让用户看到错误信息
      }
    };

    initializeEngine();
  }, []);
  
  // 更新场景实体
  useEffect(() => {
    // 这里应该将Redux中的实体数据同步到引擎场景中
    // 由于我们没有实际的引擎实现，这里只是一个示例
    console.log('Scene entities updated:', entities);
  }, [entities]);
  
  // 处理视口模式变化
  const handleViewportModeChange = (mode: string) => {
    dispatch(setViewportMode(mode as ViewportMode));
  };
  
  // 处理渲染模式变化
  const handleRenderModeChange = (e: any) => {
    dispatch(setRenderMode(e.target.value as RenderMode));
  };
  
  return (
    <div className="viewport-container" ref={containerRef}>
      {/* 顶部工具栏 - 按照参考图片设计 */}
      <div className="viewport-top-toolbar">
        <div className="toolbar-left">
          {/* 视口标签 */}
          <span className="viewport-label">{t('editor.viewport') || 'Viewport'}</span>

          {/* 变换工具组 - 与参考图片一致 */}
          <Button.Group size="small" className="transform-tools">
            <Tooltip title={t('editor.selectTool') || 'Select Tool (Q)'}>
              <Button
                type={viewportMode === 'select' ? 'primary' : 'default'}
                onClick={() => handleViewportModeChange('select')}
                icon={<AimOutlined />}
              />
            </Tooltip>
            <Tooltip title={t('editor.translateTool') || 'Translate Tool (W)'}>
              <Button
                type={viewportMode === 'translate' ? 'primary' : 'default'}
                onClick={() => handleViewportModeChange('translate')}
                icon={<DragOutlined />}
              />
            </Tooltip>
            <Tooltip title={t('editor.rotateTool') || 'Rotate Tool (E)'}>
              <Button
                type={viewportMode === 'rotate' ? 'primary' : 'default'}
                onClick={() => handleViewportModeChange('rotate')}
                icon={<RotateLeftOutlined />}
              />
            </Tooltip>
            <Tooltip title={t('editor.scaleTool') || 'Scale Tool (R)'}>
              <Button
                type={viewportMode === 'scale' ? 'primary' : 'default'}
                onClick={() => handleViewportModeChange('scale')}
                icon={<ScissorOutlined />}
              />
            </Tooltip>
          </Button.Group>
        </div>

        <div className="toolbar-center">
          {/* 渲染模式和其他控制 */}
          <Radio.Group
            value={renderMode}
            onChange={handleRenderModeChange}
            size="small"
            className="render-mode-group"
          >
            <Radio.Button value="solid">{t('editor.solid') || 'Solid'}</Radio.Button>
            <Radio.Button value="wireframe">{t('editor.wireframe') || 'Wireframe'}</Radio.Button>
            <Radio.Button value="material">{t('editor.material') || 'Material'}</Radio.Button>
          </Radio.Group>
        </div>

        <div className="toolbar-right">
          {/* 视口控制按钮 */}
          <Button.Group size="small">
            <Tooltip title={t('editor.frameSelected') || 'Frame Selected (F)'}>
              <Button icon={<BorderOutlined />} />
            </Tooltip>
            <Tooltip title={t('editor.focusCamera') || 'Focus Camera'}>
              <Button icon={<CameraOutlined />} />
            </Tooltip>
            <Tooltip title={t('editor.fullscreen') || 'Fullscreen'}>
              <Button icon={<FullscreenOutlined />} />
            </Tooltip>
          </Button.Group>
        </div>
      </div>

      {/* 3D画布 - 主要渲染区域 */}
      <canvas
        ref={canvasRef}
        className="viewport-canvas"
        style={{
          width: '100%',
          height: 'calc(100% - 40px)',
          background: 'linear-gradient(to bottom, #87CEEB 0%, #F4A460 100%)', // 模拟沙漠天空背景
          display: 'block'
        }}
      />
    </div>
  );
};

export default Viewport;
