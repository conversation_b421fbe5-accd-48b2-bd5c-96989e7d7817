/**
 * 项目状态切片
 */
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { apiClient } from '../../services/ApiClient';

// 后端项目实体类型（与后端保持一致）
export interface BackendProject {
  id: string;
  name: string;
  description: string;
  thumbnailUrl?: string;
  visibility: 'public' | 'private';
  ownerId: string;
  isTemplate: boolean;
  isArchived: boolean;
  createdAt: string;
  updatedAt: string;
}

// 前端项目类型（转换后的格式）
export interface Project {
  id: string;
  name: string;
  description: string;
  thumbnail?: string;
  createdAt: string;
  updatedAt: string;
  isPublic: boolean;
  ownerId: string;
  isTemplate?: boolean;
  isArchived?: boolean;
  scenes?: Scene[]; // 添加场景列表字段
}

// 定义场景类型
export interface Scene {
  id: string;
  name: string;
  description?: string;
  thumbnail?: string;
  createdAt: string;
  updatedAt: string;
  projectId: string;
}

// 转换函数：将后端项目格式转换为前端格式
const transformBackendProject = (backendProject: BackendProject): Project => {
  return {
    id: backendProject.id,
    name: backendProject.name,
    description: backendProject.description,
    thumbnail: backendProject.thumbnailUrl,
    createdAt: backendProject.createdAt,
    updatedAt: backendProject.updatedAt,
    isPublic: backendProject.visibility === 'public',
    ownerId: backendProject.ownerId,
    isTemplate: backendProject.isTemplate,
    isArchived: backendProject.isArchived,
  };
};

// 定义项目状态
interface ProjectState {
  projects: Project[];
  currentProject: Project | null;
  currentScene: Scene | null;
  isLoading: boolean;
  error: string | null;
}

// 初始状态
const initialState: ProjectState = {
  projects: [],
  currentProject: null,
  currentScene: null,
  isLoading: false,
  error: null};

// 获取项目列表
export const fetchProjects = createAsyncThunk('project/fetchProjects', async (_, { rejectWithValue }) => {
  try {
    const response = await apiClient.get('/projects');
    const backendProjects: BackendProject[] = response.data;
    // 转换为前端格式
    return backendProjects.map(transformBackendProject);
  } catch (error: any) {
    // 对于网络错误或服务不可用，返回友好的错误信息
    if (!error.response) {
      return rejectWithValue('网络连接失败，请检查网络连接');
    }

    if (error.response.status >= 500) {
      return rejectWithValue('服务暂时不可用，请稍后重试');
    }

    if (error.response.status === 401) {
      return rejectWithValue('登录已过期，请重新登录');
    }

    return rejectWithValue(error.response?.data?.message || '获取项目列表失败');
  }
});

// 获取项目详情
export const fetchProjectById = createAsyncThunk(
  'project/fetchProjectById',
  async (projectId: string, { rejectWithValue }) => {
    try {
      const response = await apiClient.get(`/projects/${projectId}`);
      const backendProject: BackendProject = response.data;
      // 转换为前端格式
      return transformBackendProject(backendProject);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取项目详情失败');
    }
  }
);

// 创建新项目
export const createProject = createAsyncThunk(
  'project/createProject',
  async (
    { name, description, isPublic = false }: { name: string; description: string; isPublic?: boolean },
    { rejectWithValue }
  ) => {
    try {
      // 转换前端格式到后端格式
      const backendData = {
        name,
        description,
        visibility: isPublic ? 'public' : 'private'
      };
      const response = await apiClient.post('/projects', backendData);
      const backendProject: BackendProject = response.data;
      // 转换为前端格式
      return transformBackendProject(backendProject);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '创建项目失败');
    }
  }
);

// 更新项目
export const updateProject = createAsyncThunk(
  'project/updateProject',
  async (
    {
      projectId,
      data}: {
      projectId: string;
      data: { name?: string; description?: string; isPublic?: boolean };
    },
    { rejectWithValue }
  ) => {
    try {
      // 转换前端格式到后端格式
      const backendData: any = {};
      if (data.name !== undefined) backendData.name = data.name;
      if (data.description !== undefined) backendData.description = data.description;
      if (data.isPublic !== undefined) backendData.visibility = data.isPublic ? 'public' : 'private';

      const response = await apiClient.patch(`/projects/${projectId}`, backendData);
      const backendProject: BackendProject = response.data;
      // 转换为前端格式
      return transformBackendProject(backendProject);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '更新项目失败');
    }
  }
);

// 删除项目
export const deleteProject = createAsyncThunk(
  'project/deleteProject',
  async (projectId: string, { rejectWithValue }) => {
    try {
      if (!projectId) {
        throw new Error('项目ID不能为空');
      }

      console.log('开始删除项目:', projectId);

      const response = await apiClient.delete(`/projects/${projectId}`);

      console.log('项目删除成功:', projectId);
      return projectId;
    } catch (error: any) {
      console.error('删除项目失败:', error);

      // 提取错误信息
      let errorMessage = '删除项目失败';

      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      } else if (error.response?.status === 401) {
        errorMessage = '认证失败，请重新登录';
      } else if (error.response?.status === 403) {
        errorMessage = '您没有权限删除此项目';
      } else if (error.response?.status === 404) {
        errorMessage = '项目不存在或已被删除';
      } else if (error.response?.status >= 500) {
        errorMessage = '服务器错误，请稍后重试';
      }

      return rejectWithValue(errorMessage);
    }
  }
);

// 获取项目场景列表
export const fetchProjectScenes = createAsyncThunk(
  'project/fetchProjectScenes',
  async (projectId: string, { rejectWithValue }) => {
    try {
      console.log('正在获取项目场景列表，项目ID:', projectId);
      const response = await apiClient.get(`/projects/${projectId}/scenes`);
      console.log('场景列表获取成功:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('获取项目场景失败:', error);

      // 详细的错误处理
      if (!error.response) {
        // 网络错误
        return rejectWithValue('网络连接失败，请检查网络连接');
      }

      if (error.response.status >= 500) {
        // 服务器错误
        return rejectWithValue('服务暂时不可用，请稍后重试');
      }

      if (error.response.status === 401 || error.response.status === 403) {
        // 认证/权限错误
        return rejectWithValue('权限不足，请重新登录');
      }

      if (error.response.status === 404) {
        // 项目不存在或没有场景
        console.log('项目不存在或没有场景，返回空数组');
        return []; // 返回空数组而不是错误
      }

      // 其他错误
      return rejectWithValue(error.response?.data?.message || '获取项目场景失败');
    }
  }
);

// 创建场景
export const createScene = createAsyncThunk(
  'project/createScene',
  async (
    {
      projectId,
      name,
      description}: {
      projectId: string;
      name: string;
      description?: string;
    },
    { rejectWithValue }
  ) => {
    try {
      const response = await apiClient.post(`/projects/${projectId}/scenes`, { name, description });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '创建场景失败');
    }
  }
);

// 创建项目切片
const projectSlice = createSlice({
  name: 'project',
  initialState,
  reducers: {
    setCurrentProject: (state, action: PayloadAction<Project>) => {
      state.currentProject = action.payload;
    },
    setCurrentScene: (state, action: PayloadAction<Scene>) => {
      state.currentScene = action.payload;
    },
    clearCurrentProject: (state) => {
      state.currentProject = null;
      state.currentScene = null;
    },
    clearError: (state) => {
      state.error = null;
    }},
  extraReducers: (builder) => {
    // 获取项目列表
    builder
      .addCase(fetchProjects.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchProjects.fulfilled, (state, action) => {
        state.isLoading = false;
        state.projects = action.payload;
      })
      .addCase(fetchProjects.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // 获取项目详情
    builder
      .addCase(fetchProjectById.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchProjectById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentProject = action.payload;
      })
      .addCase(fetchProjectById.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // 创建项目
    builder
      .addCase(createProject.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createProject.fulfilled, (state, action) => {
        state.isLoading = false;
        state.projects.push(action.payload);
        state.currentProject = action.payload;
      })
      .addCase(createProject.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // 更新项目
    builder
      .addCase(updateProject.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateProject.fulfilled, (state, action) => {
        state.isLoading = false;
        // 确保projects数组存在
        if (!Array.isArray(state.projects)) {
          state.projects = [];
        }
        const index = state.projects.findIndex((p) => p && p.id === action.payload.id);
        if (index !== -1) {
          state.projects[index] = action.payload;
        }
        if (state.currentProject?.id === action.payload.id) {
          state.currentProject = action.payload;
        }
      })
      .addCase(updateProject.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // 删除项目
    builder
      .addCase(deleteProject.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteProject.fulfilled, (state, action) => {
        state.isLoading = false;
        // 确保projects数组存在
        if (!Array.isArray(state.projects)) {
          state.projects = [];
        }
        state.projects = state.projects.filter((p) => p && p.id !== action.payload);
        if (state.currentProject?.id === action.payload) {
          state.currentProject = null;
          state.currentScene = null;
        }
      })
      .addCase(deleteProject.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // 获取项目场景
    builder
      .addCase(fetchProjectScenes.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchProjectScenes.fulfilled, (state, action) => {
        state.isLoading = false;
        const scenes = action.payload;

        // 更新当前项目的场景列表
        if (state.currentProject) {
          state.currentProject.scenes = scenes;
        }
      })
      .addCase(fetchProjectScenes.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // 创建场景
    builder
      .addCase(createScene.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createScene.fulfilled, (state, action) => {
        state.isLoading = false;
        const newScene = action.payload;

        // 设置当前场景
        state.currentScene = newScene;

        // 将新场景添加到当前项目的场景列表中
        if (state.currentProject) {
          if (!state.currentProject.scenes) {
            state.currentProject.scenes = [];
          }
          state.currentProject.scenes.push(newScene);
        }
      })
      .addCase(createScene.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  }});

export const { setCurrentProject, setCurrentScene, clearCurrentProject, clearError } = projectSlice.actions;
export default projectSlice.reducer;
