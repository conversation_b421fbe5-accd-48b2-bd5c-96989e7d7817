/**
 * 可停靠面板布局组件
 * 基于rc-dock库实现灵活的面板布局系统
 */
import React, { useRef, useEffect, forwardRef, useImperativeHandle } from 'react';
import { DockLayout as RcDockLayout, LayoutData, TabData } from 'rc-dock';
import { useAppDispatch, useAppSelector } from '../../store';
import { setLayout, saveLayoutToStorage, loadLayoutFromStorage } from '../../store/ui/layoutSlice';
import { createPanelContent } from '../panels/PanelRegistry';
import 'rc-dock/dist/rc-dock.css';
import './DockLayout.less';

interface DockLayoutProps {
  defaultLayout: LayoutData;
  onLayoutChange?: (layout: LayoutData) => void;
}

export interface DockLayoutRef {
  saveLayout: () => any;
  loadLayout: (layout: LayoutData) => void;
}

const DockLayout = forwardRef<DockLayoutRef, DockLayoutProps>(({
  defaultLayout,
  onLayoutChange
}, ref) => {
  const dispatch = useAppDispatch();
  const { layout, theme } = useAppSelector((state) => state.ui);
  const dockLayoutRef = useRef<RcDockLayout>(null);

  // 暴露给父组件的方法
  useImperativeHandle(ref, () => ({
    saveLayout: () => {
      if (dockLayoutRef.current) {
        return dockLayoutRef.current.saveLayout();
      }
      return defaultLayout;
    },
    loadLayout: (newLayout: LayoutData) => {
      if (dockLayoutRef.current) {
        dockLayoutRef.current.loadLayout(newLayout);
      }
    }
  }), [defaultLayout]);

  // 初始化布局
  useEffect(() => {
    // 尝试从本地存储加载布局
    dispatch(loadLayoutFromStorage());
  }, [dispatch]);

  // 处理布局变化
  const handleLayoutChange = (newLayout: LayoutData) => {
    dispatch(setLayout(newLayout));

    // 保存布局到本地存储
    dispatch(saveLayoutToStorage(newLayout));

    // 调用外部传入的布局变化回调
    if (onLayoutChange) {
      onLayoutChange(newLayout);
    }
  };

  // 获取当前使用的布局，并转换字符串内容为React组件
  const processedLayout = React.useMemo(() => {
    const processLayout = (layoutData: LayoutData): LayoutData => {
      // 深拷贝布局数据，避免修改只读属性
      const deepClone = (obj: any): any => {
        if (obj === null || typeof obj !== 'object') {
          return obj;
        }

        if (obj instanceof Array) {
          return obj.map(item => deepClone(item));
        }

        const cloned: any = {};
        for (const key in obj) {
          if (obj.hasOwnProperty(key)) {
            cloned[key] = deepClone(obj[key]);
          }
        }
        return cloned;
      };

      const processChildren = (children: any[]): any[] => {
        if (!Array.isArray(children)) {
          console.warn('⚠️ processChildren: children不是数组', children);
          return [];
        }

        return children.map(child => {
          // 检查child是否为null或undefined
          if (!child || typeof child !== 'object') {
            console.warn('⚠️ processChildren: 无效的child对象', child);
            return null;
          }

          // 深拷贝子节点，避免修改原始对象
          const clonedChild = deepClone(child);

          if (clonedChild.tabs && Array.isArray(clonedChild.tabs)) {
            // 处理标签页
            clonedChild.tabs = clonedChild.tabs.map((tab: any) => {
              if (!tab || typeof tab !== 'object') {
                console.warn('⚠️ processChildren: 无效的tab对象', tab);
                return null;
              }

              if (typeof tab.content === 'string') {
                try {
                  return {
                    ...tab,
                    content: createPanelContent(tab.content)
                  };
                } catch (error) {
                  console.error(`❌ 创建面板内容失败: ${tab.content}`, error);
                  return {
                    ...tab,
                    content: <div style={{ padding: '16px', color: '#f56565' }}>面板加载失败: {tab.content}</div>
                  };
                }
              }
              return tab;
            }).filter(tab => tab !== null); // 过滤掉null值
          } else if (clonedChild.children && Array.isArray(clonedChild.children)) {
            // 递归处理子节点
            clonedChild.children = processChildren(clonedChild.children);
          }
          return clonedChild;
        }).filter(child => child !== null); // 过滤掉null值
      };

      // 深拷贝整个布局数据
      const result = deepClone(layoutData);

      // 安全检查和处理dockbox
      if (result.dockbox && typeof result.dockbox === 'object') {
        if (Array.isArray(result.dockbox.children)) {
          result.dockbox.children = processChildren(result.dockbox.children);
        } else {
          console.warn('⚠️ dockbox.children不是数组', result.dockbox.children);
          result.dockbox.children = [];
        }
      } else {
        console.warn('⚠️ 布局数据缺少dockbox', result);
        result.dockbox = { mode: 'horizontal', children: [] };
      }

      // 安全检查和处理floatbox
      if (result.floatbox && typeof result.floatbox === 'object') {
        if (Array.isArray(result.floatbox.children)) {
          result.floatbox.children = processChildren(result.floatbox.children);
        } else if (result.floatbox.children) {
          console.warn('⚠️ floatbox.children不是数组', result.floatbox.children);
          result.floatbox.children = [];
        }
      }

      return result;
    };

    const currentLayout = layout || defaultLayout;

    // 安全检查当前布局
    if (!currentLayout || typeof currentLayout !== 'object') {
      console.warn('⚠️ 当前布局无效，使用默认布局', currentLayout);
      return processLayout(defaultLayout);
    }

    try {
      return processLayout(currentLayout);
    } catch (error) {
      console.error('❌ 处理布局失败，使用默认布局', error);
      return processLayout(defaultLayout);
    }
  }, [layout, defaultLayout]);

  return (
    <div className={`dock-layout-container ${theme === 'dark' ? 'dark-theme' : 'light-theme'}`}>
      <RcDockLayout
        ref={dockLayoutRef}
        defaultLayout={processedLayout}
        style={{ width: '100%', height: '100%' }}
        onLayoutChange={handleLayoutChange}
      />
    </div>
  );
});

export default DockLayout;
