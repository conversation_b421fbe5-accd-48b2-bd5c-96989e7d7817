/**
 * 编辑器入口文件
 */
import React from 'react';
import ReactDOM from 'react-dom/client';
import { Provider } from 'react-redux';
import { <PERSON>rowserRouter } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import { I18nextProvider } from 'react-i18next';
import zhCN from 'antd/locale/zh_CN';
import i18n from './i18n';
import { I18nDiagnostics } from './utils/i18nDiagnostics';

import App from './App';
import { store } from './store';
import './styles/index.less';

// 错误边界组件
class ErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('应用错误:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div style={{
          padding: '20px',
          textAlign: 'center',
          backgroundColor: '#f5f5f5',
          height: '100vh',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center'
        }}>
          <h1>应用加载失败</h1>
          <p>请刷新页面重试，或联系技术支持。</p>
          <button
            onClick={() => window.location.reload()}
            style={{
              padding: '10px 20px',
              backgroundColor: '#1890ff',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            刷新页面
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

// 确保DOM元素存在
const rootElement = document.getElementById('root');
if (!rootElement) {
  throw new Error('Root element not found');
}

// 创建React根节点并渲染应用
const root = ReactDOM.createRoot(rootElement);

// 将store添加到全局对象以便错误处理使用
(window as any).__REDUX_STORE__ = store;

// 全局错误处理
window.addEventListener('error', (event) => {
  console.error('全局错误:', event.error);

  // 特别处理null访问错误
  if (event.error && event.error.message) {
    const message = event.error.message;
    if (message.includes('Cannot read properties of null') ||
        message.includes('Cannot read property') ||
        message.includes('reading \'x\'') ||
        message.includes('reading \'y\'')) {
      console.error('❌ 检测到null属性访问错误:', message);
      console.error('📍 错误位置:', event.filename, '行:', event.lineno, '列:', event.colno);

      // 尝试重置相关状态
      try {
        // 清理可能的问题状态
        const store = (window as any).__REDUX_STORE__;
        if (store) {
          console.log('🔄 尝试重置UI状态...');
          store.dispatch({ type: 'ui/closeContextMenu' });
        }
      } catch (resetError) {
        console.warn('⚠️ 状态重置失败:', resetError);
      }
    }
  }

  // 防止错误导致应用完全崩溃
  event.preventDefault();
});

window.addEventListener('unhandledrejection', (event) => {
  console.error('未处理的Promise拒绝:', event.reason);
  // 防止未处理的Promise拒绝导致应用崩溃
  event.preventDefault();
});

// 强制初始化i18n并等待完成
const initializeI18nAndRender = async () => {
  try {
    // 确保i18n完全初始化
    if (!i18n.isInitialized) {
      console.log('🔄 等待i18n初始化...');
      await new Promise((resolve, reject) => {
        if (i18n.isInitialized) {
          resolve(true);
          return;
        }

        const timeout = setTimeout(() => {
          reject(new Error('i18n初始化超时'));
        }, 10000); // 10秒超时

        i18n.on('initialized', () => {
          clearTimeout(timeout);
          resolve(true);
        });

        i18n.on('failedLoading', (lng, ns, msg) => {
          clearTimeout(timeout);
          reject(new Error(`i18n加载失败: ${lng}/${ns} - ${msg}`));
        });
      });
    }

    // 强制设置为中文
    if (i18n.language !== 'zh-CN') {
      console.log('🔧 强制设置语言为中文');
      await i18n.changeLanguage('zh-CN');
    }

    console.log('✅ i18n初始化完成');
    console.log('🌐 当前语言:', i18n.language);
    console.log('📦 可用资源:', Object.keys(i18n.store?.data || {}));

    // 运行 i18n 诊断（仅在开发环境）
    if (process.env.NODE_ENV === 'development') {
      console.log('\n');
      I18nDiagnostics.printDiagnosticReport();
      console.log('\n');
    }

    // 测试翻译功能
    const testTranslation = i18n.t('loginTitle', { ns: 'auth' });
    console.log('🧪 测试翻译 auth:loginTitle:', testTranslation);

    // 渲染应用
    root.render(
      <ErrorBoundary>
        <I18nextProvider i18n={i18n}>
          <Provider store={store}>
            <BrowserRouter>
              <ConfigProvider locale={zhCN}>
                <App />
              </ConfigProvider>
            </BrowserRouter>
          </Provider>
        </I18nextProvider>
      </ErrorBoundary>
    );

    console.log('✅ 应用渲染成功');

  } catch (error) {
    console.error('❌ 应用启动失败:', error);

    // 显示错误信息
    root.render(
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        flexDirection: 'column',
        fontFamily: 'Arial, sans-serif'
      }}>
        <h1 style={{ color: '#ff4d4f', marginBottom: '16px' }}>应用启动失败</h1>
        <p style={{ color: '#666', marginBottom: '16px' }}>请刷新页面重试</p>
        <pre style={{
          background: '#f5f5f5',
          padding: '16px',
          borderRadius: '4px',
          fontSize: '12px',
          maxWidth: '600px',
          overflow: 'auto'
        }}>
          {error.toString()}
        </pre>
        <button
          onClick={() => window.location.reload()}
          style={{
            marginTop: '16px',
            padding: '10px 20px',
            backgroundColor: '#1890ff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          刷新页面
        </button>
      </div>
    );
  }
};

// 启动应用
initializeI18nAndRender();
