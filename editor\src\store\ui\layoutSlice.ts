/**
 * 布局状态管理
 */
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { LayoutData } from 'rc-dock';
import { PanelType } from './uiSlice';

// 定义布局状态接口
export interface LayoutState {
  layout: LayoutData | null;
  savedLayouts: Record<string, LayoutData>;
  activeLayout: string;
  theme: 'light' | 'dark';
}

// 默认布局配置
// 注意：content 字段在实际使用时会被转换为 React 元素
// 布局结构:左侧(层级+材质库) + 中间(场景视图+底部资源面板) + 右侧(模型+属性)
// 符合参考图片的四区域布局:顶部工具栏 + 左侧面板 + 中间视口 + 右侧面板 + 底部资源面板
export const defaultLayout: LayoutData = {
  dockbox: {
    mode: 'horizontal',
    children: [
      // 左侧面板:层级(Hierarchy)和材质库(Material Library)
      {
        mode: 'vertical',
        size: 250,
        children: [
          {
            size: 400,
            tabs: [
              { id: PanelType.HIERARCHY, title: 'Hierarchy', content: PanelType.HIERARCHY as any, closable: false }
            ]
          },
          {
            size: 200,
            tabs: [
              { id: 'material-library', title: 'Material Library', content: PanelType.ASSETS as any, closable: false }
            ]
          }
        ]
      },
      // 中间区域:场景视图(上) + 资源面板(下)
      {
        mode: 'vertical',
        size: 650,
        children: [
          {
            size: 500,
            tabs: [
              { id: PanelType.SCENE, title: 'View port', content: PanelType.SCENE as any, closable: false }
            ]
          },
          // 底部资源面板 - 横跨中间区域
          {
            size: 250,
            tabs: [
              { id: PanelType.ASSETS, title: 'Assets', content: PanelType.ASSETS as any, closable: false },
              { id: 'visual-scripting', title: 'Visual Scripting', content: PanelType.CONSOLE as any, closable: true }
            ]
          }
        ]
      },
      // 右侧面板:模型(Models)和属性(Properties)
      {
        mode: 'vertical',
        size: 300,
        children: [
          {
            size: 200,
            tabs: [
              { id: 'models', title: 'Models', content: PanelType.INSTANCES as any, closable: false }
            ]
          },
          {
            size: 400,
            tabs: [
              { id: PanelType.INSPECTOR, title: 'Properties', content: PanelType.INSPECTOR as any, closable: false }
            ]
          }
        ]
      }
    ]
  }
};

// 预定义布局配置
export const predefinedLayouts: Record<string, LayoutData> = {
  default: defaultLayout,
  // 调试布局:增加调试面板和控制台
  debug: {
    dockbox: {
      mode: 'horizontal',
      children: [
        {
          mode: 'vertical',
          size: 250,
          children: [
            {
              size: 300,
              tabs: [
                { id: PanelType.HIERARCHY, title: 'Hierarchy', content: PanelType.HIERARCHY as any, closable: false }
              ]
            }
          ]
        },
        {
          mode: 'vertical',
          size: 650,
          children: [
            {
              size: 400,
              tabs: [
                { id: PanelType.SCENE, title: 'View port', content: PanelType.SCENE as any, closable: false }
              ]
            },
            {
              size: 200,
              tabs: [
                { id: PanelType.DEBUG, title: 'Debug', content: PanelType.DEBUG as any, closable: true },
                { id: PanelType.CONSOLE, title: 'Console', content: PanelType.CONSOLE as any, closable: true }
              ]
            }
          ]
        },
        {
          mode: 'vertical',
          size: 300,
          children: [
            {
              size: 400,
              tabs: [
                { id: PanelType.INSPECTOR, title: 'Properties', content: PanelType.INSPECTOR as any, closable: false }
              ]
            }
          ]
        }
      ]
    }
  },
  // 编码布局:增加控制台和资源面板
  coding: {
    dockbox: {
      mode: 'horizontal',
      children: [
        {
          mode: 'vertical',
          size: 250,
          children: [
            {
              size: 300,
              tabs: [
                { id: PanelType.HIERARCHY, title: 'Hierarchy', content: PanelType.HIERARCHY as any, closable: false }
              ]
            }
          ]
        },
        {
          mode: 'vertical',
          size: 650,
          children: [
            {
              size: 400,
              tabs: [
                { id: PanelType.SCENE, title: 'View port', content: PanelType.SCENE as any, closable: false }
              ]
            },
            {
              size: 200,
              tabs: [
                { id: PanelType.CONSOLE, title: 'Console', content: PanelType.CONSOLE as any, closable: true },
                { id: PanelType.ASSETS, title: 'Assets', content: PanelType.ASSETS as any, closable: false }
              ]
            }
          ]
        },
        {
          mode: 'vertical',
          size: 300,
          children: [
            {
              size: 400,
              tabs: [
                { id: PanelType.INSPECTOR, title: 'Properties', content: PanelType.INSPECTOR as any, closable: false }
              ]
            }
          ]
        }
      ]
    }
  },
  // 最小化布局:仅显示层级和场景视图
  minimal: {
    dockbox: {
      mode: 'horizontal',
      children: [
        {
          size: 250,
          tabs: [
            { id: PanelType.HIERARCHY, title: 'Hierarchy', content: PanelType.HIERARCHY as any, closable: false }
          ]
        },
        {
          size: 950,
          tabs: [
            { id: PanelType.SCENE, title: 'View port', content: PanelType.SCENE as any, closable: false }
          ]
        }
      ]
    }
  }
};

// 初始状态
const initialState: LayoutState = {
  layout: null,
  savedLayouts: predefinedLayouts,
  activeLayout: 'default',
  theme: 'light'
};

// 创建布局切片
const layoutSlice = createSlice({
  name: 'layout',
  initialState,
  reducers: {
    // 设置当前布局
    setLayout: (state, action: PayloadAction<LayoutData>) => {
      state.layout = action.payload;
    },

    // 保存布局
    saveLayout: (state, action: PayloadAction<{ name: string; layout: LayoutData }>) => {
      const { name, layout } = action.payload;
      state.savedLayouts[name] = layout;
      state.activeLayout = name;
    },

    // 加载布局
    loadLayout: (state, action: PayloadAction<string>) => {
      const layoutName = action.payload;
      if (state.savedLayouts[layoutName]) {
        state.layout = state.savedLayouts[layoutName];
        state.activeLayout = layoutName;
      }
    },

    // 删除保存的布局
    deleteLayout: (state, action: PayloadAction<string>) => {
      const layoutName = action.payload;
      if (layoutName !== 'default' && state.savedLayouts[layoutName]) {
        delete state.savedLayouts[layoutName];
        if (state.activeLayout === layoutName) {
          state.activeLayout = 'default';
          state.layout = state.savedLayouts.default;
        }
      }
    },

    // 重置为默认布局
    resetLayout: (state) => {
      state.layout = defaultLayout;
      state.activeLayout = 'default';

      // 保存到本地存储
      try {
        localStorage.setItem('dl-engine-editor-layout', JSON.stringify(defaultLayout));
        localStorage.setItem('dl-engine-editor-active-layout', 'default');
      } catch (error) {
        console.error('保存布局到本地存储失败:', error);
      }
    },

    // 切换主题
    toggleTheme: (state) => {
      state.theme = state.theme === 'light' ? 'dark' : 'light';
    },

    // 保存布局到本地存储
    saveLayoutToStorage: (state, action: PayloadAction<LayoutData>) => {
      try {
        localStorage.setItem('dl-engine-editor-layout', JSON.stringify(action.payload));
        localStorage.setItem('dl-engine-editor-saved-layouts', JSON.stringify(state.savedLayouts));
        localStorage.setItem('dl-engine-editor-active-layout', state.activeLayout);
      } catch (error) {
        console.error('保存布局到本地存储失败:', error);
      }
    },

    // 从本地存储加载布局
    loadLayoutFromStorage: (state) => {
      try {
        const savedLayout = localStorage.getItem('dl-engine-editor-layout');
        const savedLayouts = localStorage.getItem('dl-engine-editor-saved-layouts');
        const activeLayout = localStorage.getItem('dl-engine-editor-active-layout');

        if (savedLayout) {
          state.layout = JSON.parse(savedLayout);
        } else {
          state.layout = defaultLayout;
        }

        if (savedLayouts) {
          state.savedLayouts = JSON.parse(savedLayouts);
        }

        if (activeLayout) {
          state.activeLayout = activeLayout;
        }
      } catch (error) {
        console.error('从本地存储加载布局失败:', error);
        state.layout = defaultLayout;
      }
    }
  }
});

// 导出actions
export const {
  setLayout,
  saveLayout,
  loadLayout,
  deleteLayout,
  resetLayout,
  toggleTheme,
  saveLayoutToStorage,
  loadLayoutFromStorage
} = layoutSlice.actions;

// 导出reducer
export default layoutSlice.reducer;
